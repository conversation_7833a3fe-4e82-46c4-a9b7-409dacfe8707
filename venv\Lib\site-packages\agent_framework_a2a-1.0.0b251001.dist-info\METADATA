Metadata-Version: 2.4
Name: agent-framework-a2a
Version: 1.0.0b251001
Summary: A2A integration for Microsoft Agent Framework.
Author-email: Microsoft <<EMAIL>>
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Classifier: License :: OSI Approved :: MIT License
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Typing :: Typed
License-File: LICENSE
Requires-Dist: agent-framework-core
Requires-Dist: a2a-sdk>=0.3.5
Project-URL: homepage, https://aka.ms/agent-framework
Project-URL: issues, https://github.com/microsoft/agent-framework/issues
Project-URL: release_notes, https://github.com/microsoft/agent-framework/releases?q=tag%3Apython-1&expanded=true
Project-URL: source, https://github.com/microsoft/agent-framework/tree/main/python

# Get Started with Microsoft Agent Framework A2A

Please install this package via pip:

```bash
pip install agent-framework-a2a
```

## A2A Agent Integration

The A2A agent integration enables communication with remote A2A-compliant agents using the standardized A2A protocol. This allows your Agent Framework applications to connect to agents running on different platforms, languages, or services.

### Basic Usage Example

See the [A2A agent examples](https://github.com/microsoft/agent-framework/tree/main/python/samples/getting_started/agents/a2a/) which demonstrate:

- Connecting to remote A2A agents
- Sending messages and receiving responses
- Handling different content types (text, files, data)
- Streaming responses and real-time interaction


