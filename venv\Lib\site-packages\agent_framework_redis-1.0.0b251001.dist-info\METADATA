Metadata-Version: 2.4
Name: agent-framework-redis
Version: 1.0.0b251001
Summary: Redis integration for Microsoft Agent Framework.
Author-email: Microsoft <<EMAIL>>
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Classifier: License :: OSI Approved :: MIT License
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Typing :: Typed
License-File: LICENSE
Requires-Dist: agent-framework-core
Requires-Dist: redis>=6.4.0
Requires-Dist: redisvl>=0.8.2
Requires-Dist: numpy>=2.2.6
Project-URL: homepage, https://aka.ms/agent-framework
Project-URL: issues, https://github.com/microsoft/agent-framework/issues
Project-URL: release_notes, https://github.com/microsoft/agent-framework/releases?q=tag%3Apython-1&expanded=true
Project-URL: source, https://github.com/microsoft/agent-framework/tree/main/python

# Get Started with Microsoft Agent Framework Redis

Please install this package via pip:

```bash
pip install agent-framework-redis
```

## Components

### Memory Context Provider

The `RedisProvider` enables persistent context & memory capabilities for your agents, allowing them to remember user preferences and conversation context across sessions and threads.

#### Basic Usage Examples

Review the set of [getting started examples](../../samples/getting_started/context_providers/redis/README.md) for using the Redis context provider.

### Redis Chat Message Store

The `RedisChatMessageStore` provides persistent conversation storage using Redis Lists, enabling chat history to survive application restarts and support distributed applications.

#### Key Features

- **Persistent Storage**: Messages survive application restarts
- **Thread Isolation**: Each conversation thread has its own Redis key
- **Message Limits**: Configurable automatic trimming of old messages
- **Serialization Support**: Full compatibility with Agent Framework thread serialization
- **Production Ready**: Connection pooling, error handling, and performance optimized

#### Basic Usage Examples

See the complete [Redis chat message store examples](../../samples/getting_started/threads/redis_chat_message_store_thread.py) including:
- User session management
- Conversation persistence across restarts  
- Thread serialization and deserialization
- Automatic message trimming
- Error handling patterns

### Installing and running Redis

You have 3 options to set-up Redis:

#### Option A: Local Redis with Docker
```bash
docker run --name redis -p 6379:6379 -d redis:8.0.3
```

#### Option B: Redis Cloud
Get a free db at https://redis.io/cloud/

#### Option C: Azure Managed Redis
Here's a quickstart guide to create **Azure Managed Redis** for as low as $12 monthly: https://learn.microsoft.com/en-us/azure/redis/quickstart-create-managed-redis

