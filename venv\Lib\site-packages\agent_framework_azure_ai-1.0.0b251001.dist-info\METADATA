Metadata-Version: 2.4
Name: agent-framework-azure-ai
Version: 1.0.0b251001
Summary: Azure AI Foundry integration for Microsoft Agent Framework.
Author-email: Microsoft <<EMAIL>>
Requires-Python: >=3.10
Description-Content-Type: text/markdown
Classifier: License :: OSI Approved :: MIT License
Classifier: Development Status :: 4 - Beta
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Typing :: Typed
License-File: LICENSE
Requires-Dist: agent-framework-core
Requires-Dist: azure-ai-projects >= 1.0.0b11
Requires-Dist: azure-ai-agents == 1.2.0b5
Requires-Dist: aiohttp ~= 3.8
Project-URL: homepage, https://aka.ms/agent-framework
Project-URL: issues, https://github.com/microsoft/agent-framework/issues
Project-URL: release_notes, https://github.com/microsoft/agent-framework/releases?q=tag%3Apython-1&expanded=true
Project-URL: source, https://github.com/microsoft/agent-framework/tree/main/python

# Get Started with Microsoft Agent Framework Azure AI

Please install this package via pip:

```bash
pip install agent-framework-azure-ai
```

and see the [README](https://github.com/microsoft/agent-framework/tree/main/python/README.md) for more information.

