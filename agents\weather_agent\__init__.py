"""Weather Agent - A simple agent that provides weather information."""

from agent_framework import Chat<PERSON><PERSON>
from agent_framework.openai import OpenAIChatClient
import requests


def get_weather(location: str) -> str:
    """Get weather information for a specific location using wttr.in service.
    
    Args:
        location: City or location to retrieve the weather for
        
    Returns:
        Weather information as a string
    """
    try:
        # Use wttr.in API to get weather data
        url = f"https://wttr.in/{location}?format=3"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        return response.text.strip()
    except Exception as e:
        return f"Sorry, I couldn't get weather information for {location}. Error: {str(e)}"


# Create the agent instance
agent = ChatAgent(
    name="WeatherAgent",
    description="A helpful agent that provides current weather information for any location using wttr.in service.",
    chat_client=OpenAIChatClient(),
    tools=[get_weather]
)
