"""Weather Agent - A simple agent that provides weather information."""

from agent_framework import ChatAgent
import requests
import os


def get_weather(location: str) -> str:
    """Get weather information for a specific location using wttr.in service.

    Args:
        location: City or location to retrieve the weather for

    Returns:
        Weather information as a string
    """
    try:
        # Use wttr.in API to get weather data
        url = f"https://wttr.in/{location}?format=3"
        response = requests.get(url, timeout=10)
        response.raise_for_status()
        return response.text.strip()
    except Exception as e:
        return f"Sorry, I couldn't get weather information for {location}. Error: {str(e)}"


def get_detailed_weather(location: str) -> str:
    """Get detailed weather forecast for a specific location.

    Args:
        location: City or location to retrieve the detailed weather for

    Returns:
        Detailed weather forecast as a string
    """
    try:
        # Use wttr.in API to get detailed weather data
        url = f"https://wttr.in/{location}?format=v2"
        response = requests.get(url, timeout=15)
        response.raise_for_status()
        return response.text.strip()
    except Exception as e:
        return f"Sorry, I couldn't get detailed weather information for {location}. Error: {str(e)}"


# Try to create agent with OpenAI if API key is available, otherwise create a simple function-based agent
try:
    from agent_framework.openai import OpenAIChatClient

    # Check if API key is available
    api_key = os.getenv('OPENAI_API_KEY')
    if api_key and api_key != 'your_actual_openai_api_key_here':
        # Create the agent instance with OpenAI
        agent = ChatAgent(
            name="WeatherAgent",
            description="A helpful agent that provides current weather information for any location using wttr.in service.",
            chat_client=OpenAIChatClient(),
            tools=[get_weather, get_detailed_weather]
        )
    else:
        # Create a simple agent without OpenAI
        from agent_framework import Agent

        class SimpleWeatherAgent(Agent):
            def __init__(self):
                super().__init__(
                    name="WeatherAgent",
                    description="A helpful agent that provides current weather information for any location using wttr.in service."
                )

            def process_request(self, request: str) -> str:
                """Process weather requests directly."""
                # Simple keyword matching for weather requests
                request_lower = request.lower()

                if 'weather' in request_lower or 'temperature' in request_lower or 'forecast' in request_lower:
                    # Try to extract location from the request
                    words = request.split()
                    location = None

                    # Look for location after common prepositions
                    for i, word in enumerate(words):
                        if word.lower() in ['in', 'for', 'at', 'of'] and i + 1 < len(words):
                            location = ' '.join(words[i+1:])
                            break

                    if not location:
                        # If no preposition found, try to find a capitalized word (likely a place name)
                        for word in words:
                            if word[0].isupper() and word.lower() not in ['weather', 'temperature', 'forecast', 'what', 'how', 'is', 'the']:
                                location = word
                                break

                    if location:
                        if 'detailed' in request_lower or 'forecast' in request_lower:
                            return get_detailed_weather(location)
                        else:
                            return get_weather(location)
                    else:
                        return "Please specify a location for the weather information. For example: 'What's the weather in London?'"
                else:
                    return "I'm a weather agent. I can provide weather information for any location. Try asking: 'What's the weather in [city name]?'"

        agent = SimpleWeatherAgent()

except ImportError:
    # Fallback if OpenAI client is not available
    from agent_framework import Agent

    class SimpleWeatherAgent(Agent):
        def __init__(self):
            super().__init__(
                name="WeatherAgent",
                description="A helpful agent that provides current weather information for any location using wttr.in service."
            )

        def process_request(self, request: str) -> str:
            """Process weather requests directly."""
            return "Weather agent is available but requires proper setup. Please check the configuration."

    agent = SimpleWeatherAgent()
