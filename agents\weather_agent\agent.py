"""Weather Agent implementation."""

import requests
from typing import Optional


class WeatherService:
    """Service for fetching weather information."""
    
    def __init__(self):
        self.base_url = "https://wttr.in"
    
    def get_current_weather(self, location: str, format_type: str = "3") -> str:
        """
        Get current weather for a location.
        
        Args:
            location: The city or location name
            format_type: Format type for wttr.in (default: "3" for simple format)
            
        Returns:
            Weather information as string
        """
        try:
            url = f"{self.base_url}/{location}"
            params = {"format": format_type}
            
            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            weather_data = response.text.strip()
            if not weather_data:
                return f"No weather data available for {location}"
                
            return weather_data
            
        except requests.exceptions.RequestException as e:
            return f"Error fetching weather for {location}: {str(e)}"
        except Exception as e:
            return f"Unexpected error getting weather for {location}: {str(e)}"
    
    def get_detailed_weather(self, location: str) -> str:
        """
        Get detailed weather forecast for a location.
        
        Args:
            location: The city or location name
            
        Returns:
            Detailed weather information as string
        """
        try:
            url = f"{self.base_url}/{location}"
            params = {"format": "v2"}  # Verbose format
            
            response = requests.get(url, params=params, timeout=15)
            response.raise_for_status()
            
            return response.text.strip()
            
        except requests.exceptions.RequestException as e:
            return f"Error fetching detailed weather for {location}: {str(e)}"
        except Exception as e:
            return f"Unexpected error getting detailed weather for {location}: {str(e)}"


# Global weather service instance
weather_service = WeatherService()


def get_weather(location: str) -> str:
    """
    Get current weather information for a specific location.
    
    This function uses the wttr.in service to fetch real-time weather data.
    
    Args:
        location: City or location to retrieve the weather for
        
    Returns:
        Current weather information as a string
    """
    return weather_service.get_current_weather(location)


def get_detailed_weather(location: str) -> str:
    """
    Get detailed weather forecast for a specific location.
    
    This function provides a more comprehensive weather report including
    forecast information.
    
    Args:
        location: City or location to retrieve the detailed weather for
        
    Returns:
        Detailed weather forecast as a string
    """
    return weather_service.get_detailed_weather(location)
